<!DOCTYPE html>
<html dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="<?php echo \Illuminate\Support\Arr::toCssClasses(['dark' => ($appearance ?? 'system') == 'dark']); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        
        <script>
            (function() {
                const appearance = '<?php echo e($appearance ?? "system"); ?>';

                if (appearance === 'system') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    if (prefersDark) {
                        document.documentElement.classList.add('dark');
                    }
                }
            })();
        </script>

        
        <style>
            html {
                background-color: oklch(1 0 0);
            }

            html.dark {
                background-color: oklch(0.141 0.005 285.823);
            }
        </style>

        <title inertia><?php echo e(config('app.name', 'Laravel')); ?></title>

        <link rel="icon" href="/favicon.ico" sizes="any">
        <link rel="icon" href="/favicon.svg" type="image/svg+xml">
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">

        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

        <?php echo app('Tighten\Ziggy\BladeRouteGenerator')->generate(); ?>
        <?php echo app('Illuminate\Foundation\Vite')->reactRefresh(); ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/js/app.tsx', "resources/js/pages/{$page['component']}.tsx"]); ?>
        <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->head; } ?>
    </head>
    <body class="font-sans antialiased">
        <?php if (!isset($__inertiaSsrDispatched)) { $__inertiaSsrDispatched = true; $__inertiaSsrResponse = app(\Inertia\Ssr\Gateway::class)->dispatch($page); }  if ($__inertiaSsrResponse) { echo $__inertiaSsrResponse->body; } else { ?><div id="app" data-page="<?php echo e(json_encode($page)); ?>"></div><?php } ?>
        <?php if (isset($component)) { $__componentOriginal089b1ff04f7784e3920290e8d6fa65d8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal089b1ff04f7784e3920290e8d6fa65d8 = $attributes; } ?>
<?php $component = App\View\Components\Translations::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('translations'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Translations::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal089b1ff04f7784e3920290e8d6fa65d8)): ?>
<?php $attributes = $__attributesOriginal089b1ff04f7784e3920290e8d6fa65d8; ?>
<?php unset($__attributesOriginal089b1ff04f7784e3920290e8d6fa65d8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal089b1ff04f7784e3920290e8d6fa65d8)): ?>
<?php $component = $__componentOriginal089b1ff04f7784e3920290e8d6fa65d8; ?>
<?php unset($__componentOriginal089b1ff04f7784e3920290e8d6fa65d8); ?>
<?php endif; ?>
    </body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\projects\ecommerce\resources\views/app.blade.php ENDPATH**/ ?>