@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 14 84% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 24 63% 88%;
    --secondary-foreground: 14 84% 20%;

    --muted: 24 20% 95%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 24 63% 88%;
    --accent-foreground: 14 84% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 24 20% 90%;
    --input: 24 20% 90%;
    --ring: 14 84% 60%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 14 20% 8%;
    --foreground: 24 63% 88%;

    --card: 14 20% 8%;
    --card-foreground: 24 63% 88%;

    --popover: 14 20% 8%;
    --popover-foreground: 24 63% 88%;

    --primary: 14 84% 60%;
    --primary-foreground: 0 0% 100%;

    --secondary: 14 20% 15%;
    --secondary-foreground: 24 63% 88%;

    --muted: 14 20% 15%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 14 20% 15%;
    --accent-foreground: 24 63% 88%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 14 20% 15%;
    --input: 14 20% 15%;
    --ring: 14 84% 60%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-arabic;
    font-feature-settings:
      "liga" 1,
      "calt" 1;
  }

  html[dir="rtl"] {
    direction: rtl;
  }

  html[dir="ltr"] {
    direction: ltr;
  }

  .rtl {
    direction: rtl;
  }

  /* Arabic text optimization */
  .arabic-text {
    font-family: "Noto Sans Arabic", system-ui, sans-serif;
    text-align: right;
    direction: rtl;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .moroccan-gradient {
    background: linear-gradient(135deg, #f4b8a6 0%, #e2724a 50%, #d0572e 100%);
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .shadow-moroccan {
    box-shadow:
      0 10px 25px -5px rgba(226, 114, 74, 0.1),
      0 8px 10px -6px rgba(226, 114, 74, 0.1);
  }
}
