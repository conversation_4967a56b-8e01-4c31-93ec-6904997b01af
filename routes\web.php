<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\User\DashboardController;


Route::get('/locale/{locale}', function (string $locale) {

    if (! in_array($locale, ['en', 'ar', 'fr'])) {
        abort(400);
    }
    
    session()->put('locale', $locale);

    return back();
})->name('locale');

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // // User Dashboard Routes
    //ignore this routes now 
    // Route::prefix('user')->as('user.')->group(function () {
    //     Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    //     // Placeholder routes for other user dashboard pages
    //     Route::get('profile', function () {
    //         return Inertia::render('user/profile');
    //     })->name('profile');

    //     Route::get('orders', function () {
    //         return Inertia::render('user/orders');
    //     })->name('orders');

    //     Route::get('wishlist', function () {
    //         return Inertia::render('user/wishlist');
    //     })->name('wishlist');

    //     Route::get('payments', function () {
    //         return Inertia::render('user/payments');
    //     })->name('payments');

    //     Route::get('notifications', function () {
    //         return Inertia::render('user/notifications');
    //     })->name('notifications');

    //     Route::get('settings', function () {
    //         return Inertia::render('user/settings');
    //     })->name('settings');

    //     Route::get('help', function () {
    //         return Inertia::render('user/help');
    //     })->name('help');
    // });
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
require __DIR__ . '/admin.php';
