---
description: Deploy the app to production using Netlify.
globs: 
alwaysApply: true
---
Deploying the app to production is powered by Netlify, however, it's only possible as long as there are several tools from netlify available:

- if the tools are not available, tell the user to connect the netlify MCP in: https://builder.io/app/mcp-servers

If the user asks to deploy the app, use the appropiete netlify MCP tools to deploy the app.

Notice that, netlify builds the source code in their own servers, so while it's a good idea to check if `npm run build` works locally, it's not a requirement.
