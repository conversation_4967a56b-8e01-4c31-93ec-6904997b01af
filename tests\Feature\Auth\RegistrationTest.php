<?php

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('registration screen can be rendered', function () {
    $response = $this->get('/register');

    $response->assertStatus(200);
});

test('new users can register', function () {
    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'phone' => '+**********',
        'gender' => 'male',
        'national_id' => 'ABC123456',
        'country' => 'Morocco',
        'city' => 'Casablanca',
        'payment_method' => 'bank',
        'bank_name' => 'Test Bank',
        'rib_number' => '****************',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ]);

    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard', absolute: false));
});

test('registration requires all fields', function () {
    $response = $this->post('/register', []);

    $response->assertSessionHasErrors([
        'first_name',
        'last_name',
        'phone',
        'gender',
        'national_id',
        'country',
        'city',
        'payment_method',
        'bank_name',
        'rib_number',
        'email',
        'password',
    ]);
});

test('registration validates unique fields', function () {
    // Create a user first
    $existingUser = \App\Models\User::factory()->create([
        'email' => '<EMAIL>',
        'phone' => '+**********',
        'national_id' => 'ABC123456',
        'rib_number' => '****************',
    ]);

    $response = $this->post('/register', [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'phone' => '+**********', // Duplicate
        'gender' => 'male',
        'national_id' => 'ABC123456', // Duplicate
        'country' => 'Morocco',
        'city' => 'Casablanca',
        'payment_method' => 'bank',
        'bank_name' => 'Test Bank',
        'rib_number' => '****************', // Duplicate
        'email' => '<EMAIL>', // Duplicate
        'password' => 'password123',
        'password_confirmation' => 'password123',
    ]);

    $response->assertSessionHasErrors([
        'email',
        'phone',
        'national_id',
        'rib_number',
    ]);
});