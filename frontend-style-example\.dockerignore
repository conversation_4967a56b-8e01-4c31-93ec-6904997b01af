*~
*.log
*.lock
*.tmp
*.tmp.*
log.txt

.DS_Store
node_modules
**/node_modules/**
build
data
.env
load-ids.txt

dist
server
tmp
types
.git
.gitignore
dist
service
tests
fixtures-pages
fixtures-apps

# Netlify
.netlify
packages/ml-air/lib
packages/ml-air/bin
packages/ml-air/project
packages/ml-air/share
packages/ml-air/random_forest_classification/
packages/ml-air/__pycache__/
packages/ml-air/app/__pycache__/
src/_tests_/dataset-comparision.csv
packages/vcp-common/native-bridge/build
packages/vcp-common/_tests_/dataset-ranking.csv
node_modules/
Dockerfile
.gitignore